from fastapi import <PERSON>AP<PERSON>, Request
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
import os
import uvicorn
from BASE.http.chat import chat_stream
from  BASE.http.watchdog import watch_dog
from BASE.http.kb.delete_kb import delete_kb_source
from BASE.http.kb.list_kbs import list_knowledge_bases
from BASE.http.swagger import swagger_list
from BASE.http.auto_actions import auto_actions


app = FastAPI(title="CodeMate HTTP API")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)


@app.post("/chat/stream")
async def stream(request: Request):
    data = await request.json()
    llm_ = {
        "api_base": data.get("base_url", "https://llm.codemate.ai/v1"),
        "api_key": data.get("api_key", ""),
        "model": data.get("model", "bodh-x1"),
    }
    return StreamingResponse(chat_stream(llm_=llm_, messages=data["messages"], call_for="chat"), media_type="text/event-stream")


@app.post("/delete/kb")
async def delete_kb(request: Request):
    data = await request.json()
    kbid = data.get("kbid", "")
    source = data.get("source", "both")

    return JSONResponse(delete_kb_source(kbid, source=source))



@app.post("/list/kb")
async def list_kb(request: Request):
    data = await request.json()
    include_cloud = data.get("include_cloud", False)
    session = request.headers.get("x-session")

    result = await list_knowledge_bases(include_cloud=include_cloud, session=session)
    return JSONResponse(result)


@app.post("/swagger/list")
async def get_swagger_list(request: Request):
    data = await request.json()
    return JSONResponse(await swagger_list(data))


@app.post("/watchdog/file_delete")
async def watchdog_file_delete(request: Request):
    data = await request.json()
    file_path = data.get("file_path", "")
    if not file_path:
        return JSONResponse({"status": "error", "message": "File path not provided"})

    return JSONResponse(watch_dog(file_path=file_path, call_for="delete"))


@app.post("/watchdog/file_update")
async def watchdog_file_delete(request: Request):
    data = await request.json()
    file_path = data.get("file_path", "")
    if not file_path:
        return JSONResponse({"status": "error", "message": "File path not provided"})

    return JSONResponse(watch_dog(file_path=file_path, call_for="update"))


@app.post("/auto-actions/{operation}")
async def auto_actions_handler(request: Request, operation: str):
    data = await request.json()
    session_id = request.headers.get("x-session")
    return JSONResponse(auto_actions(data, session_id=session_id, operation=operation))


if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=45213)